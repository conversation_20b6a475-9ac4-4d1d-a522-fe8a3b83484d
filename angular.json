{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "cli": {"analytics": false, "warnings": {"versionMismatch": false}}, "newProjectRoot": "projects", "projects": {"receetoProject": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/receeto-project", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.css"], "scripts": [], "allowedCommonJsDependencies": ["canvg", "rgbcolor", "raf", "core-js", "html2canvas", "dompurify", "locate-path", "find-up", "pkg-dir"], "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": {"inline": true}}, "vendorChunk": true, "extractLicenses": false, "buildOptimizer": true, "sourceMap": false, "namedChunks": true, "aot": true, "crossOrigin": "anonymous"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "50kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "optimization": true}, "optimized": {"buildOptimizer": true, "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": true}, "fonts": {"inline": true}}, "vendorChunk": true, "extractLicenses": false, "sourceMap": false, "namedChunks": true, "aot": true, "crossOrigin": "anonymous", "subresourceIntegrity": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "defaultConfiguration": "development", "options": {"buildTarget": "receetoProject:build", "hmr": true, "liveReload": true, "poll": 2000, "allowedHosts": ["."], "host": "localhost", "open": true}, "configurations": {"production": {"buildTarget": "receetoProject:build:production"}, "development": {"buildTarget": "receetoProject:build:development"}, "optimized": {"buildTarget": "receetoProject:build:optimized"}}}}}, "dashboard": {"projectType": "application", "root": "projects/dashboard", "sourceRoot": "projects/dashboard/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/dashboard", "index": "projects/dashboard/src/index.html", "main": "projects/dashboard/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/dashboard/tsconfig.app.json", "assets": ["projects/dashboard/src/favicon.ico", "projects/dashboard/src/assets"], "styles": ["projects/dashboard/src/styles.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "projects/dashboard/src/environments/environment.ts", "with": "projects/dashboard/src/environments/environment.prod.ts"}], "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "optimization": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"development": {"buildTarget": "dashboard:build:development"}, "production": {"buildTarget": "dashboard:build:production"}}, "options": {"buildTarget": "dashboard:build"}}}}, "analytics": {"projectType": "application", "root": "projects/analytics", "sourceRoot": "projects/analytics/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/analytics", "index": "projects/analytics/src/index.html", "main": "projects/analytics/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/analytics/tsconfig.app.json", "assets": ["projects/analytics/src/favicon.ico", "projects/analytics/src/assets"], "styles": ["projects/analytics/src/styles.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "projects/analytics/src/environments/environment.ts", "with": "projects/analytics/src/environments/environment.prod.ts"}], "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "optimization": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"development": {"buildTarget": "analytics:build:development"}, "production": {"buildTarget": "analytics:build:production"}}, "options": {"buildTarget": "analytics:build"}}}}}}