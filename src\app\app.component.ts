import { Component, OnInit, inject } from '@angular/core';
import { RouterOutlet, Router, NavigationEnd } from '@angular/router';
import { AuthService } from './core/auth/auth.service';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet],
  templateUrl: './app.component.html',
  styles: []
})
export class AppComponent implements OnInit {
  private authService = inject(AuthService);
  private router = inject(Router);

  ngOnInit() {
    // Handle role-based redirection when user navigates to root or welcome page
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event) => {
      const navigationEvent = event as NavigationEnd;
      const currentUser = this.authService.currentUser();

      // If user is authenticated and on welcome page, redirect to appropriate dashboard
      if (currentUser && navigationEvent.url === '/welcome-to-receeto') {
        if (currentUser.role === 'shopper') {
          this.router.navigate(['/shopper-dashboard']);
        } else if (currentUser.role === 'seller') {
          this.router.navigate(['/seller/dashboard']);
        }
      }
    });
  }
}

