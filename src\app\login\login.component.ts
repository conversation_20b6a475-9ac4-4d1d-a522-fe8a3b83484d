import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { AuthService } from '../core/auth/auth.service';
import { Login } from '../interfaces/login';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {
  loginData: Login = {
    email: '',
    password: ''
  };
  showPassword = false;
  errorMessage = '';
  role: string = '';

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private authService: AuthService
  ) {}

  ngOnInit() {
    this.route.queryParams.subscribe(params => {
      this.role = params['role'] || '';
    });
  }

  togglePassword() {
    this.showPassword = !this.showPassword;
  }

  onLogin() {
    if (this.loginData.email && this.loginData.password) {
      this.authService.login(this.loginData).subscribe({
        next: (user) => {
          console.log('Login successful:', user);
          this.errorMessage = '';

          // Navigate to the appropriate dashboard based on role
          const userRole = user.role || this.role;
          if (userRole === 'shopper') {
            this.router.navigate(['/shopper-dashboard']);
          } else if (userRole === 'seller') {
            this.router.navigate(['/seller/dashboard']);
          } else {
            // Default to shopper dashboard for unknown roles
            this.router.navigate(['/shopper-dashboard']);
          }
        },
        error: (err) => {
          console.error('Login error:', err);
          this.errorMessage = err.message || 'An error occurred during login. Please try again.';
        }
      });
    } else {
      this.errorMessage = 'Please fill in all required fields.';
    }
  }
}